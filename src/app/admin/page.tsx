"use client";

import { useEffect, useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

import { Role } from "@/core.constants";
import { useRootContext } from "@/root-context";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { CollectionManagement } from "./collection-management";
import { FeesManagement } from "./fees-management";
import { RevenueDisplay } from "./revenue-display";

export default function Admin() {
  const { role } = useRootContext();
  const [isOpen, setIsOpen] = useState(true);

  useEffect(() => {
    document.documentElement.classList.add("dark");
  }, []);

  if (role !== Role.ADMIN) return null;

  return (
    <div className="mx-auto w-full max-w-6xl p-4 space-y-4">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="flex w-full justify-between p-4 hover:bg-gray-800"
          >
            <span className="text-lg font-semibold">
              Revenue & Fees Management
            </span>
            {isOpen ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <RevenueDisplay />
            <FeesManagement />
          </div>
        </CollapsibleContent>
      </Collapsible>

      <CollectionManagement />
    </div>
  );
}
